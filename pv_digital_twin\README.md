# 光伏数字孪生平台 - Django 版本

## 项目简介

本项目是光伏系统数字孪生平台的 Django 实现版本，通过 Web 界面展示光伏系统的仿真结果，包括系统输出功率、环境条件、故障诊断等功能。项目使用 ECharts 进行数据可视化，提供实时监控和分析功能。

## 主要功能

- **系统仪表盘**：展示系统概况、输出功率、环境条件、每日发电量、性能分析等。
- **故障诊断**：异常检测、误差分析、性能预测和对比。
- **系统设置**：参数配置、数据导入导出、系统维护等。

## 技术栈

- **后端**：Django 4.2
- **前端**：Bootstrap 5、ECharts 5
- **数据处理**：Pandas、NumPy
- **仿真模型**：PVlib、自定义光伏数字孪生模型
- **异常检测**：基于 LSTM 和 KAN 的异常检测模型

## 安装步骤

1. 克隆项目并进入项目目录

```bash
git clone <仓库地址>
cd pv_digital_twin
```

2. 创建并激活虚拟环境（推荐）

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/MacOS
source venv/bin/activate
```

3. 安装依赖

```bash
pip install -r requirements.txt
```

4. 数据库初始化

```bash
python manage.py migrate
```

5. 运行开发服务器

```bash
python manage.py runserver
```

6. 访问应用

在浏览器中访问：`http://127.0.0.1:8000/`

## 项目结构

```
pv_digital_twin/
├── api/                   # API应用，提供数据接口
├── dashboard/             # 仪表盘应用，提供Web界面
│   ├── templates/         # 页面模板
│   └── pv_model_adapter.py # PV模型适配器
├── static/                # 静态文件
├── templates/             # 基础模板
├── pv_digital_twin/       # 项目主设置
├── manage.py              # Django管理脚本
└── requirements.txt       # 项目依赖
```

## 使用说明

### 仪表盘

仪表盘显示系统的实时状态，包括当前功率、累计发电量、环境条件等信息。图表会自动更新，展示最近 48 小时的数据。

### 故障诊断

故障诊断页面展示系统异常情况，包括检测到的异常点、预测误差分析和功率对比图表。系统会自动分析异常并提供建议措施。

### 系统设置

系统设置页面允许配置光伏系统的参数，如位置、容量、温度系数等。修改设置后，系统会重新运行仿真并更新数据。

## 开发指南

### 添加新功能

1. 在相应的应用目录中创建新的视图函数
2. 更新 URL 配置
3. 创建模板文件
4. 如需添加新的 API 端点，在 api 应用中定义

### 修改仿真模型

PV 模型适配器（`dashboard/pv_model_adapter.py`）是连接 Django 应用和光伏仿真模型的桥梁。若需修改仿真逻辑，请在此文件中进行调整。

## 许可证

[指定许可证类型]

## 联系方式

[提供联系信息]
